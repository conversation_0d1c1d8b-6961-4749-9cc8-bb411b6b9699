'use client';

import React, { forwardRef, useCallback, useMemo, useState } from 'react';
import { MentionsInput, Mention } from 'react-mentions';
import { cn } from '@/lib/utils';
import { useMCPToolClassification, ClassifiedMCPTool } from '@/hooks/use-mcp-tool-classification';
import { Check, Plus, ExternalLink, Zap, Loader2 } from 'lucide-react';

interface ToolMentionsInputProps {
  value: string;
  onChange: (value: string) => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  selectedAgentId?: string;
  isDraggingOver?: boolean;
  onToolConnect?: (tool: ClassifiedMCPTool) => Promise<void>;
  onToolAddToAgent?: (tool: ClassifiedMCPTool) => Promise<void>;
}

interface MentionData {
  id: string;
  display: string;
  tool: ClassifiedMCPTool;
}

export const ToolMentionsInput = forwardRef<HTMLTextAreaElement, ToolMentionsInputProps>(
  (
    {
      value,
      onChange,
      onKeyDown,
      placeholder = 'Type @ to mention tools...',
      disabled = false,
      className,
      selectedAgentId,
      isDraggingOver = false,
      onToolConnect,
      onToolAddToAgent,
    },
    ref
  ) => {
    const { allTools, isLoading } = useMCPToolClassification(selectedAgentId);
    const [actionLoading, setActionLoading] = useState<string | null>(null); // Track which tool is loading

    // Convert ALL MCP tools to mention data format
    const mentionData = useMemo((): MentionData[] => {
      return allTools.map((tool) => ({
        id: tool.id,
        display: tool.displayName,
        tool,
      }));
    }, [allTools]);

    // Handle mention input change
    const handleChange = useCallback(
      (_event: any, newValue: string) => {
        onChange(newValue);
      },
      [onChange]
    );

    // Function to find the current mention being typed
    const getCurrentMention = useCallback((text: string, cursorPosition: number) => {
      // Look for @ symbol before cursor position
      const beforeCursor = text.substring(0, cursorPosition);
      const lastAtIndex = beforeCursor.lastIndexOf('@');

      if (lastAtIndex === -1) return null;

      // Get text after @ symbol
      const afterAt = text.substring(lastAtIndex + 1);
      const spaceIndex = afterAt.indexOf(' ');
      const mentionText = spaceIndex === -1 ? afterAt : afterAt.substring(0, spaceIndex);

      // Find matching tool
      const matchingTool = allTools.find(tool =>
        tool.displayName.toLowerCase().includes(mentionText.toLowerCase()) ||
        tool.name.toLowerCase().includes(mentionText.toLowerCase())
      );

      return matchingTool ? { tool: matchingTool, mentionText, startIndex: lastAtIndex } : null;
    }, [allTools]);

    // Handle key presses for Tab/Enter on mentions
    const handleKeyDown = useCallback(async (e: React.KeyboardEvent) => {
      if ((e.key === 'Tab' || e.key === 'Enter') && !e.shiftKey) {
        const target = e.target as HTMLTextAreaElement;
        const cursorPosition = target.selectionStart;
        const currentMention = getCurrentMention(value, cursorPosition);

        if (currentMention && currentMention.tool.status !== 'connected_to_agent') {
          e.preventDefault(); // Prevent default Tab/Enter behavior

          const { tool } = currentMention;
          setActionLoading(tool.id);

          try {
            if (tool.status === 'connected_to_account' && onToolAddToAgent) {
              await onToolAddToAgent(tool);
            } else if (tool.status === 'available_to_connect' && onToolConnect) {
              await onToolConnect(tool);
            }
          } catch (error) {
            console.error('Tool action failed:', error);
          } finally {
            setActionLoading(null);
          }

          return;
        }
      }

      // Call original onKeyDown if provided
      if (onKeyDown) {
        onKeyDown(e);
      }
    }, [value, getCurrentMention, onToolConnect, onToolAddToAgent, onKeyDown]);

    // Render tool icon with clean, minimal design
    const renderToolIcon = useCallback((tool: ClassifiedMCPTool) => {
      const isIconUrl = tool.icon && tool.icon.startsWith('http');

      return (
        <div className="w-6 h-6 flex-shrink-0 flex items-center justify-center rounded-md">
          {isIconUrl ? (
            <img
              src={tool.icon}
              alt={tool.displayName}
              className="w-6 h-6 object-contain rounded-md"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                const fallback = target.nextElementSibling as HTMLElement;
                if (fallback) fallback.style.display = 'flex';
              }}
            />
          ) : null}

          {/* Fallback icon */}
          <div
            className="w-6 h-6 flex items-center justify-center text-muted-foreground"
            style={{ display: isIconUrl ? 'none' : 'flex' }}
          >
            {tool.icon && !isIconUrl ? (
              <span className="text-base">{tool.icon}</span>
            ) : (
              <Zap className="h-4 w-4" />
            )}
          </div>
        </div>
      );
    }, []);

    // Handle tool action based on status
    const handleToolAction = useCallback((tool: ClassifiedMCPTool, event?: React.MouseEvent) => {
      if (event) {
        event.preventDefault();
        event.stopPropagation();
      }

      switch (tool.status) {
        case 'connected_to_agent':
          // Tool is already connected, just add it as a mention
          return true; // Allow normal mention behavior
        case 'connected_to_account':
          // Tool is connected to account but not agent, add it to agent
          if (onToolAddToAgent) {
            onToolAddToAgent(tool);
            return false; // Prevent normal mention behavior
          }
          return true;
        case 'available_to_connect':
          // Tool needs to be connected first
          if (onToolConnect) {
            onToolConnect(tool);
            return false; // Prevent normal mention behavior
          }
          return true;
        default:
          return true;
      }
    }, [onToolConnect, onToolAddToAgent]);

    // Clean, minimal suggestion renderer with action handling
    const renderSuggestion = useCallback(
      (suggestion: MentionData, _search: string, highlightedDisplay: React.ReactNode) => {
        const { tool } = suggestion;

        const getStatusIcon = () => {
          // Show loading spinner if this tool is currently being processed
          if (actionLoading === tool.id) {
            return <Loader2 className="h-3.5 w-3.5 animate-spin text-blue-500" />;
          }

          switch (tool.status) {
            case 'connected_to_agent':
              return <Check className="h-3.5 w-3.5 text-green-500" />;
            case 'connected_to_account':
              return <Plus className="h-3.5 w-3.5 text-blue-500" />;
            case 'available_to_connect':
              return <ExternalLink className="h-3.5 w-3.5 text-orange-500" />;
            default:
              return null;
          }
        };

        const handleClick = (event: React.MouseEvent) => {
          const shouldProceed = handleToolAction(tool, event);
          if (!shouldProceed) {
            // Action was handled, don't proceed with mention
            return;
          }
          // For connected_to_agent tools, let the normal mention behavior proceed
        };

        return (
          <div
            className="flex items-center gap-3 px-3 py-2 hover:bg-muted/30 cursor-pointer transition-colors rounded-md mx-1"
            onClick={handleClick}
          >
            {/* Tool Icon */}
            {renderToolIcon(tool)}

            {/* Tool Info */}
            <div className="flex-1 min-w-0">
              <div className="font-medium text-sm text-foreground truncate">
                {highlightedDisplay}
              </div>
              {tool.toolCount && tool.toolCount > 0 && (
                <div className="text-xs text-muted-foreground">
                  {tool.toolCount} tools
                </div>
              )}
            </div>

            {/* Status Icon */}
            <div className="flex items-center flex-shrink-0">
              {getStatusIcon()}
            </div>
          </div>
        );
      },
      [renderToolIcon, handleToolAction, actionLoading]
    );

    // Custom mention display transform
    const displayTransform = useCallback((_id: string, display: string) => {
      return `@${display}`;
    }, []);

    // Clean, elegant styles for the mentions input
    const mentionsInputStyle = {
      control: {
        backgroundColor: 'transparent',
        fontSize: '16px',
        fontWeight: 'normal',
        border: 'none',
        outline: 'none',
      },
      '&multiLine': {
        control: {
          fontFamily: 'inherit',
          minHeight: '40px',
          maxHeight: '200px',
          overflow: 'auto',
          border: 'none',
          outline: 'none',
        },
        highlighter: {
          padding: '8px',
          border: 'none',
          overflow: 'hidden',
        },
        input: {
          padding: '8px',
          border: 'none',
          outline: 'none',
          backgroundColor: 'transparent',
          color: 'inherit',
          resize: 'none',
        },
      },
      suggestions: {
        list: {
          backgroundColor: 'hsl(var(--background))',
          border: '1px solid hsl(var(--border))',
          borderRadius: '12px',
          boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
          fontSize: '14px',
          maxHeight: '280px',
          maxWidth: '320px',
          overflow: 'auto',
          padding: '6px',
          zIndex: 9999,
          backdropFilter: 'blur(8px)',
        },
        item: {
          padding: '0',
          borderRadius: '8px',
          margin: '0',
          '&focused': {
            backgroundColor: 'hsl(var(--muted) / 0.5)',
          },
        },
      },
    };

    // Mention markup pattern
    const mentionMarkup = '@[__display__](__id__)';

    return (
      <div className={cn('w-full relative', className)}>

        <MentionsInput
          value={value}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled || isLoading || actionLoading !== null}
          className={cn(
            'w-full bg-transparent border-none shadow-none focus-visible:ring-0 px-2 py-1 text-base min-h-[40px] max-h-[200px] overflow-y-auto resize-none',
            isDraggingOver ? 'opacity-40' : '',
            actionLoading ? 'opacity-60' : '',
            'mentions-input'
          )}
          style={mentionsInputStyle}
          singleLine={false}
          allowSpaceInQuery={true}
          forceSuggestionsAboveCursor={false}
          suggestionsPortalHost={typeof document !== 'undefined' ? document.body : undefined}
          a11ySuggestionsListLabel="Available tools"
        >
          <Mention
            trigger="@"
            data={mentionData}
            renderSuggestion={renderSuggestion}
            displayTransform={displayTransform}
            markup={mentionMarkup}
            appendSpaceOnAdd={true}
            isLoading={isLoading}
            style={{
              backgroundColor: 'hsl(var(--primary))',
              color: 'hsl(var(--primary-foreground))',
              padding: '2px 6px',
              borderRadius: '4px',
              fontWeight: '500',
              fontSize: '14px',
            }}
          />
        </MentionsInput>
      </div>
    );
  }
);

ToolMentionsInput.displayName = 'ToolMentionsInput';
