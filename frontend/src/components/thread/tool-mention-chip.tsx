'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { AgentTool } from '@/hooks/use-agent-tools';

interface ToolMentionChipProps {
  tool: AgentTool;
  className?: string;
  variant?: 'default' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
}

export function ToolMentionChip({ 
  tool, 
  className, 
  variant = 'default',
  size = 'md' 
}: ToolMentionChipProps) {
  const sizeClasses = {
    sm: 'text-xs px-2 py-0.5 gap-1',
    md: 'text-sm px-2.5 py-1 gap-1.5',
    lg: 'text-base px-3 py-1.5 gap-2',
  };

  const iconSizes = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  return (
    <Badge
      variant={variant}
      className={cn(
        'inline-flex items-center font-medium',
        sizeClasses[size],
        'bg-primary/10 text-primary hover:bg-primary/20 border-primary/20',
        className
      )}
    >
      <span className={cn('flex-shrink-0', iconSizes[size])}>
        {tool.icon}
      </span>
      <span className="truncate">
        {tool.displayName}
      </span>
    </Badge>
  );
}

interface ToolMentionDisplayProps {
  toolId: string;
  displayName: string;
  className?: string;
  variant?: 'default' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Component for displaying tool mentions in chat messages
 * This is used when we only have the tool ID and display name from the mention markup
 */
export function ToolMentionDisplay({ 
  toolId, 
  displayName, 
  className,
  variant = 'default',
  size = 'md'
}: ToolMentionDisplayProps) {
  const sizeClasses = {
    sm: 'text-xs px-2 py-0.5 gap-1',
    md: 'text-sm px-2.5 py-1 gap-1.5',
    lg: 'text-base px-3 py-1.5 gap-2',
  };

  const iconSizes = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  // Extract tool type and get appropriate icon
  const getToolIcon = (id: string): string => {
    if (id.startsWith('agentpress_')) {
      const toolName = id.replace('agentpress_', '');
      // Map common agentpress tools to icons
      const iconMap: Record<string, string> = {
        'sb_shell_tool': '💻',
        'sb_files_tool': '📁',
        'sb_browser_tool': '🌐',
        'sb_deploy_tool': '🚀',
        'sb_expose_tool': '🔗',
        'web_search_tool': '🔍',
        'sb_vision_tool': '👁️',
        'data_providers_tool': '📊',
      };
      return iconMap[toolName] || '🔧';
    } else if (id.startsWith('mcp_')) {
      return '⚡';
    } else if (id.startsWith('custom_mcp_')) {
      return '🔧';
    }
    return '🛠️';
  };

  const icon = getToolIcon(toolId);

  return (
    <Badge
      variant={variant}
      className={cn(
        'inline-flex items-center font-medium',
        sizeClasses[size],
        'bg-primary/10 text-primary hover:bg-primary/20 border-primary/20',
        className
      )}
    >
      <span className={cn('flex-shrink-0', iconSizes[size])}>
        {icon}
      </span>
      <span className="truncate">
        {displayName}
      </span>
    </Badge>
  );
}
