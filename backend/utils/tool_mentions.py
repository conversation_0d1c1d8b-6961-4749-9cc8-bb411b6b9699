"""
Utilities for processing tool mentions in the backend
"""

import re
from typing import List, Dict, Any, Optional
from utils.logger import logger


def detect_tool_mentions(message_content: str) -> Dict[str, Any]:
    """
    Detect tool mentions in a user message and extract relevant information

    Args:
        message_content: The user message content

    Returns:
        Dictionary containing mention information
    """
    # Pattern to match tool mention instructions
    mention_pattern = r"\[USER MENTIONED TOOL: ([^-]+) - ([^\]]+)\]"

    mentions = []
    matches = re.findall(mention_pattern, message_content)

    for tool_name, instruction in matches:
        tool_name = tool_name.strip()
        instruction = instruction.strip()

        # Determine tool type from instruction
        tool_type = "unknown"
        if "MCP server" in instruction:
            tool_type = "configured_mcp"
        elif "custom MCP server" in instruction:
            tool_type = "custom_mcp"
        elif "Composio integration" in instruction:
            tool_type = "composio_mcp"

        mentions.append(
            {"tool_name": tool_name, "tool_type": tool_type, "instruction": instruction}
        )

    return {
        "has_mentions": len(mentions) > 0,
        "mentions": mentions,
        "mention_count": len(mentions),
    }


def enhance_system_prompt_with_mentions(
    system_content: str, latest_user_message: Optional[str] = None
) -> str:
    """
    Enhance the system prompt with tool mention context if mentions are detected

    Args:
        system_content: The current system prompt content
        latest_user_message: The latest user message to check for mentions

    Returns:
        Enhanced system prompt
    """
    if not latest_user_message:
        return system_content

    mention_info = detect_tool_mentions(latest_user_message)

    if not mention_info["has_mentions"]:
        return system_content

    # Add tool mention guidance to system prompt
    mention_guidance = "\n\n--- USER TOOL PREFERENCES ---\n"
    mention_guidance += (
        "The user has explicitly mentioned specific tools they want you to use.\n"
    )
    mention_guidance += (
        "When relevant to their request, prioritize using these mentioned tools:\n\n"
    )

    for mention in mention_info["mentions"]:
        tool_name = mention["tool_name"]
        tool_type = mention["tool_type"]

        mention_guidance += f"• **{tool_name}** ({tool_type}): "

        if tool_type == "configured_mcp":
            mention_guidance += (
                "Use this MCP server when applicable to the user's request.\n"
            )
        elif tool_type == "custom_mcp":
            mention_guidance += (
                "Use this custom MCP server when applicable to the user's request.\n"
            )
        elif tool_type == "composio_mcp":
            mention_guidance += (
                "Use this Composio integration when applicable to the user's request.\n"
            )
        else:
            mention_guidance += "Use this tool when applicable to the user's request.\n"

    mention_guidance += (
        "\nIMPORTANT: The user's explicit tool mentions indicate their preference. "
    )
    mention_guidance += "When their request can be fulfilled using the mentioned tools, prioritize those tools over alternatives. "
    mention_guidance += "However, still use your judgment about when the mentioned tools are actually relevant to the task.\n"

    # Log the detected mentions for debugging
    logger.info(
        f"Detected {mention_info['mention_count']} tool mentions in user message"
    )
    for mention in mention_info["mentions"]:
        logger.info(f"  - {mention['tool_name']} ({mention['tool_type']})")

    return system_content + mention_guidance


def get_mentioned_tool_names(message_content: str) -> List[str]:
    """
    Extract just the tool names from a message with tool mentions

    Args:
        message_content: The user message content

    Returns:
        List of mentioned tool names
    """
    mention_info = detect_tool_mentions(message_content)
    return [mention["tool_name"] for mention in mention_info["mentions"]]


def clean_message_for_display(message_content: str) -> str:
    """
    Remove tool mention instructions from message content for cleaner display

    Args:
        message_content: The message content with tool mention instructions

    Returns:
        Cleaned message content
    """
    # Pattern to match tool mention instructions
    mention_pattern = r"\[USER MENTIONED TOOL: [^\]]+\]"

    # Remove the mention instructions
    cleaned_content = re.sub(mention_pattern, "", message_content)

    # Clean up extra whitespace
    cleaned_content = re.sub(r"\n\s*\n", "\n\n", cleaned_content)
    cleaned_content = cleaned_content.strip()

    return cleaned_content
